{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:47:56.916Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?page=1&limit=12\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:17:56:1756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:47:56.971Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/auth/profile\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:17:57:1757"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.505Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=1\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.527Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.554Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customer-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.573Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?search=test\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Access denied. No token provided.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Access denied. No token provided.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:43:11\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:57.058Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=5\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:57:1957"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Access denied. No token provided.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Access denied. No token provided.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:43:11\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T06:21:39.441Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/health\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:51:39:5139"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:19:01.509Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:49:01:491"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:20:04.921Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:50:04:504"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"User not found.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: User not found.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:65:13\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:21:33.015Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:51:33:5133"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:21:57.260Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=1\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:51:57:5157"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:21:57.279Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:51:57:5157"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:21:57.298Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customer-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:51:57:5157"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:21:57.314Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?search=test\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:51:57:5157"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getEmployeeServiceList: operator does not exist: uuid = uuid[]\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"parameters":{},"parent":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getEmployeeServiceList (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1940:23)\n    at async Promise.all (index 1)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError details:\u001b[39m","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentEstimations: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentEstimations (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1738:25)\n    at async Promise.all (index 4)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentRmas: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentRmas (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1814:18)\n    at async Promise.all (index 7)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentLeads: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Lead.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentLeads (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1867:19)\n    at async Promise.all (index 9)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentAmcs: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AMC.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentAmcs (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1710:18)\n    at async Promise.all (index 3)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentProforma: column Proforma.discount does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Proforma.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentProforma (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1765:23)\n    at async Promise.all (index 5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentSales: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Sales.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentSales (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1840:19)\n    at async Promise.all (index 8)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:47:5347"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentRmas: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentRmas (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1814:18)\n    at async Promise.all (index 7)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getEmployeeServiceList: operator does not exist: uuid = uuid[]\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"parameters":{},"parent":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getEmployeeServiceList (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1940:23)\n    at async Promise.all (index 1)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError details:\u001b[39m","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentEstimations: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentEstimations (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1738:25)\n    at async Promise.all (index 4)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentAmcs: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AMC.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentAmcs (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1710:18)\n    at async Promise.all (index 3)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentProforma: column Proforma.discount does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Proforma.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentProforma (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1765:23)\n    at async Promise.all (index 5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentSales: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Sales.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentSales (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1840:19)\n    at async Promise.all (index 8)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentLeads: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Lead.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentLeads (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1867:19)\n    at async Promise.all (index 9)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 13:53:56:5356"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getExpenseAnalytics: there is no parameter $1\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42P02","file":"parse_expr.c","length":95,"line":"900","name":"error","position":"131","routine":"transformParamRef","severity":"ERROR","sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1"},"parameters":{},"parent":{"code":"42P02","file":"parse_expr.c","length":95,"line":"900","name":"error","position":"131","routine":"transformParamRef","severity":"ERROR","sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1"},"sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/analyticsController.js:350:28","timestamp":"2025-07-02 13:53:58:5358"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch expense analytics\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch expense analytics\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/analyticsController.js:465:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:23:58.503Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/analytics/expenses?period=30\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:53:58:5358"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching customers: invalid input value for enum enum_customers_status: \"deleted\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"parameters":{},"parent":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.rawSelect (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Customer.aggregate (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Customer.count (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Customer.findAndCountAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:70:40","timestamp":"2025-07-02 13:54:21:5421"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch customers\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch customers\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:100:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:24:21.236Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customers?page=1&limit=10&search=&category=&status=&type=&sortBy=created_at&sortOrder=desc\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:54:21:5421"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching customers: invalid input value for enum enum_customers_status: \"deleted\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"parameters":{},"parent":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.rawSelect (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Customer.aggregate (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Customer.count (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Customer.findAndCountAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:70:40","timestamp":"2025-07-02 13:54:21:5421"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch customers\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch customers\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:100:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:24:21.873Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customers?page=1&limit=10&search=&category=&status=&type=&sortBy=created_at&sortOrder=desc\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:54:21:5421"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"User not found.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: User not found.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:65:13\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:25:42.089Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=1\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:55:42:5542"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"User not found.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: User not found.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:65:13\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:25:42.099Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:55:42:5542"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"User not found.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: User not found.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:65:13\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:25:42.105Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customer-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:55:42:5542"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"User not found.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: User not found.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:65:13\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:25:42.113Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?search=test\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 13:55:42:5542"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getEmployeeServiceList: operator does not exist: uuid = uuid[]\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"parameters":{},"parent":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getEmployeeServiceList (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1940:23)\n    at async Promise.all (index 1)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:14:314"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError details:\u001b[39m","timestamp":"2025-07-02 14:03:14:314"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentEstimations: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentEstimations (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1738:25)\n    at async Promise.all (index 4)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:14:314"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentRmas: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentRmas (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1814:18)\n    at async Promise.all (index 7)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:14:314"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentLeads: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Lead.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentLeads (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1867:19)\n    at async Promise.all (index 9)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:15:315"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentProforma: column Proforma.discount does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Proforma.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentProforma (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1765:23)\n    at async Promise.all (index 5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:15:315"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentSales: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Sales.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentSales (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1840:19)\n    at async Promise.all (index 8)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:15:315"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentAmcs: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AMC.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentAmcs (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1710:18)\n    at async Promise.all (index 3)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:15:315"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching customers: invalid input value for enum enum_customers_status: \"deleted\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"parameters":{},"parent":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.rawSelect (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Customer.aggregate (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Customer.count (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Customer.findAndCountAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:70:40","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch customers\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch customers\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:100:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:33:23.194Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customers?page=1&limit=10&search=&category=&status=&type=&sortBy=created_at&sortOrder=desc\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getEmployeeServiceList: operator does not exist: uuid = uuid[]\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"parameters":{},"parent":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getEmployeeServiceList (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1940:23)\n    at async Promise.all (index 1)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError details:\u001b[39m","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentEstimations: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentEstimations (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1738:25)\n    at async Promise.all (index 4)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentRmas: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentRmas (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1814:18)\n    at async Promise.all (index 7)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentAmcs: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AMC.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentAmcs (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1710:18)\n    at async Promise.all (index 3)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentProforma: column Proforma.discount does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Proforma.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentProforma (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1765:23)\n    at async Promise.all (index 5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentSales: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Sales.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentSales (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1840:19)\n    at async Promise.all (index 8)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentLeads: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Lead.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentLeads (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1867:19)\n    at async Promise.all (index 9)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching customers: invalid input value for enum enum_customers_status: \"deleted\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"parameters":{},"parent":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.rawSelect (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Customer.aggregate (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Customer.count (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Customer.findAndCountAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:70:40","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch customers\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch customers\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:100:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:33:23.766Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customers?page=1&limit=10&search=&category=&status=&type=&sortBy=created_at&sortOrder=desc\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:03:23:323"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getExpenseAnalytics: there is no parameter $1\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42P02","file":"parse_expr.c","length":95,"line":"900","name":"error","position":"131","routine":"transformParamRef","severity":"ERROR","sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1"},"parameters":{},"parent":{"code":"42P02","file":"parse_expr.c","length":95,"line":"900","name":"error","position":"131","routine":"transformParamRef","severity":"ERROR","sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1"},"sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/analyticsController.js:350:28","timestamp":"2025-07-02 14:03:26:326"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch expense analytics\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch expense analytics\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/analyticsController.js:465:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:33:26.334Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/analytics/expenses?period=30\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:03:26:326"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getEmployeeServiceList: operator does not exist: uuid = uuid[]\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"parameters":{},"parent":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getEmployeeServiceList (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1940:23)\n    at async Promise.all (index 1)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:33:333"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError details:\u001b[39m","timestamp":"2025-07-02 14:03:33:333"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentEstimations: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentEstimations (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1738:25)\n    at async Promise.all (index 4)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:33:333"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentRmas: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentRmas (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1814:18)\n    at async Promise.all (index 7)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:33:333"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentLeads: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Lead.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentLeads (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1867:19)\n    at async Promise.all (index 9)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:34:334"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentProforma: column Proforma.discount does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Proforma.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentProforma (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1765:23)\n    at async Promise.all (index 5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:34:334"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentAmcs: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AMC.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentAmcs (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1710:18)\n    at async Promise.all (index 3)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:34:334"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentSales: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Sales.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentSales (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1840:19)\n    at async Promise.all (index 8)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:03:34:334"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getExpenseAnalytics: there is no parameter $1\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42P02","file":"parse_expr.c","length":95,"line":"900","name":"error","position":"131","routine":"transformParamRef","severity":"ERROR","sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1"},"parameters":{},"parent":{"code":"42P02","file":"parse_expr.c","length":95,"line":"900","name":"error","position":"131","routine":"transformParamRef","severity":"ERROR","sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1"},"sql":"SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount\n      FROM expenses\n      WHERE company_id = $1","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/analyticsController.js:350:28","timestamp":"2025-07-02 14:03:36:336"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch expense analytics\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch expense analytics\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/analyticsController.js:465:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:33:36.902Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/analytics/expenses?period=30\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:03:36:336"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching customers: invalid input value for enum enum_customers_status: \"deleted\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"parameters":{},"parent":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.rawSelect (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Customer.aggregate (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Customer.count (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Customer.findAndCountAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:70:40","timestamp":"2025-07-02 14:03:36:336"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch customers\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch customers\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:100:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:33:36.908Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customers?page=1&limit=10&search=&category=&status=&type=&sortBy=created_at&sortOrder=desc\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:03:36:336"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Validation failed\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Validation failed\\n    at validateRequest (file:///D:/New_TrackNew/track_new/backend/src/middleware/validation.js:23:11)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\route.js:149:13)\\n    at middleware (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express-validator\\\\lib\\\\middlewares\\\\check.js:16:13)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 400,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:34:00.132Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/services?page=1&limit=10&search=&status=&priority=&category=&technician=&customer=&overdue=false\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError fetching customers: invalid input value for enum enum_customers_status: \"deleted\"\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"parameters":{},"parent":{"code":"22P02","file":"enum.c","length":116,"line":"129","name":"error","position":"126","routine":"enum_in","severity":"ERROR","sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));"},"sql":"SELECT count(*) AS \"count\" FROM \"customers\" AS \"Customer\" WHERE (\"Customer\".\"deleted_at\" IS NULL AND (\"Customer\".\"status\" != 'deleted' AND \"Customer\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.rawSelect (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Customer.aggregate (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Customer.count (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1306:20)\n    at async Promise.all (index 0)\n    at async Customer.findAndCountAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1322:27)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:70:40","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Failed to fetch customers\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Failed to fetch customers\\n    at file:///D:/New_TrackNew/track_new/backend/src/controllers/customerController.js:100:11\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 500,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:34:00.143Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customers?page=1&limit=100\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\",\u001b[39m\n\u001b[31m    \"userId\": \"fce8b418-d90b-4f18-a0e0-40347e7c62d6\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getEmployeeServiceList: operator does not exist: uuid = uuid[]\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"parameters":{},"parent":{"code":"42883","file":"parse_oper.c","hint":"No operator matches the given name and argument types. You might need to add explicit type casts.","length":199,"line":"635","name":"error","position":"408","routine":"op_error","severity":"ERROR","sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC"},"sql":"SELECT\n        u.id as employer_id,\n        u.name as employer_name,\n        COUNT(CASE WHEN s.status IN ('1', '8', '9') THEN 1 END) as pending_services,\n        COUNT(CASE WHEN s.status IN ('5', '7', '10') THEN 1 END) as completed_services,\n        COUNT(CASE WHEN s.status = '6' THEN 1 END) as canceled_services,\n        COUNT(s.id) as total_services\n      FROM users u\n      LEFT JOIN services s ON u.id = s.assigned_to AND s.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      WHERE u.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND u.user_type = 'service_engineer' AND u.status = 'active'\n      GROUP BY u.id, u.name\n      ORDER BY total_services DESC","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getEmployeeServiceList (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1940:23)\n    at async Promise.all (index 1)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError details:\u001b[39m","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentRmas: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5"},"sql":"SELECT r.*, c.first_name as customer_name, c.contact_number as customer_phone\n      FROM rmas r\n      LEFT JOIN customers c ON r.customer_id = c.id\n      WHERE r.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY r.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentRmas (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1814:18)\n    at async Promise.all (index 7)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentAmcs: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"1290","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"AMC\".\"id\", \"AMC\".\"amc_code\" AS \"amcCode\", \"AMC\".\"title\", \"AMC\".\"amc_details\" AS \"description\", \"AMC\".\"start_date\" AS \"startDate\", \"AMC\".\"end_date\" AS \"endDate\", \"AMC\".\"contract_value\" AS \"contractValue\", \"AMC\".\"paid_amount\" AS \"paidAmount\", \"AMC\".\"balance_amount\" AS \"balanceAmount\", \"AMC\".\"payment_type\" AS \"paymentType\", \"AMC\".\"payment_status\" AS \"paymentStatus\", \"AMC\".\"status\", \"AMC\".\"service_frequency\" AS \"serviceFrequency\", \"AMC\".\"number_of_services\" AS \"numberOfServices\", \"AMC\".\"services_completed\" AS \"servicesCompleted\", \"AMC\".\"next_service_date\" AS \"nextServiceDate\", \"AMC\".\"last_service_date\" AS \"lastServiceDate\", \"AMC\".\"auto_renewal\" AS \"autoRenewal\", \"AMC\".\"renewal_notification_days\" AS \"renewalNotificationDays\", \"AMC\".\"terms\", \"AMC\".\"notes\", \"AMC\".\"attachments\", \"AMC\".\"service_data\" AS \"serviceData\", \"AMC\".\"customer_id\" AS \"customerId\", \"AMC\".\"company_id\" AS \"companyId\", \"AMC\".\"assigned_to\" AS \"assignedTo\", \"AMC\".\"created_by\" AS \"createdBy\", \"AMC\".\"updated_by\" AS \"updatedBy\", \"AMC\".\"is_active\" AS \"isActive\", \"AMC\".\"created_at\" AS \"createdAt\", \"AMC\".\"updated_at\" AS \"updatedAt\", \"AMC\".\"deleted_at\" AS \"deletedAt\", \"AMC\".\"created_at\", \"AMC\".\"updated_at\", \"AMC\".\"deleted_at\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"amcs\" AS \"AMC\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"AMC\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"AMC\".\"deleted_at\" IS NULL AND \"AMC\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async AMC.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentAmcs (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1710:18)\n    at async Promise.all (index 3)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentProforma: column Proforma.discount does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":116,"line":"3716","name":"error","position":"303","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Proforma\".\"id\", \"Proforma\".\"proforma_number\" AS \"proformaNumber\", \"Proforma\".\"customer_id\" AS \"customerId\", \"Proforma\".\"company_id\" AS \"companyId\", \"Proforma\".\"proforma_date\" AS \"proformaDate\", \"Proforma\".\"valid_until\" AS \"validUntil\", \"Proforma\".\"items\", \"Proforma\".\"sub_total\" AS \"subTotal\", \"Proforma\".\"discount\", \"Proforma\".\"discount_amount\" AS \"discountAmount\", \"Proforma\".\"tax_amount\" AS \"taxAmount\", \"Proforma\".\"grand_total\" AS \"grandTotal\", \"Proforma\".\"status\", \"Proforma\".\"payment_terms\" AS \"paymentTerms\", \"Proforma\".\"delivery_terms\" AS \"deliveryTerms\", \"Proforma\".\"notes\", \"Proforma\".\"terms_and_conditions\" AS \"termsAndConditions\", \"Proforma\".\"is_billaddress\" AS \"isBillAddress\", \"Proforma\".\"shipping_address\" AS \"shippingAddress\", \"Proforma\".\"estimation_id\" AS \"estimationId\", \"Proforma\".\"sales_id\" AS \"salesId\", \"Proforma\".\"sent_at\" AS \"sentAt\", \"Proforma\".\"accepted_at\" AS \"acceptedAt\", \"Proforma\".\"rejected_at\" AS \"rejectedAt\", \"Proforma\".\"rejection_reason\" AS \"rejectionReason\", \"Proforma\".\"currency\", \"Proforma\".\"exchange_rate\" AS \"exchangeRate\", \"Proforma\".\"created_by\" AS \"createdBy\", \"Proforma\".\"updated_by\" AS \"updatedBy\", \"Proforma\".\"created_at\" AS \"createdAt\", \"Proforma\".\"updated_at\" AS \"updatedAt\", \"Proforma\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"proformas\" AS \"Proforma\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Proforma\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Proforma\".\"deleted_at\" IS NULL AND \"Proforma\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Proforma.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentProforma (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1765:23)\n    at async Promise.all (index 5)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentSales: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":122,"line":"3716","name":"error","position":"2389","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Sales\".\"id\", \"Sales\".\"sales_code\" AS \"salesCode\", \"Sales\".\"invoice_id\" AS \"invoiceId\", \"Sales\".\"invoice_to\" AS \"invoiceTo\", \"Sales\".\"invoice_type\" AS \"invoiceType\", \"Sales\".\"sales_type\" AS \"salesType\", \"Sales\".\"status\", \"Sales\".\"priority\", \"Sales\".\"sales_date\" AS \"salesDate\", \"Sales\".\"due_date\" AS \"dueDate\", \"Sales\".\"valid_until\" AS \"validUntil\", \"Sales\".\"sub_total\" AS \"subTotal\", \"Sales\".\"discount\", \"Sales\".\"discount_type\" AS \"discountType\", \"Sales\".\"discount_value\" AS \"discountValue\", \"Sales\".\"discount_amount\" AS \"discountAmount\", \"Sales\".\"due_amount\" AS \"dueAmount\", \"Sales\".\"tax_amount\" AS \"taxAmount\", \"Sales\".\"shipping\", \"Sales\".\"shipping_type\" AS \"shippingType\", \"Sales\".\"shipping_value\" AS \"shippingValue\", \"Sales\".\"shipping_amount\" AS \"shippingAmount\", \"Sales\".\"total_qty\" AS \"totalQty\", \"Sales\".\"total_amount\" AS \"totalAmount\", \"Sales\".\"grand_total\" AS \"grandTotal\", \"Sales\".\"paid_amount\" AS \"paidAmount\", \"Sales\".\"balance_amount\" AS \"balanceAmount\", \"Sales\".\"return_amount\" AS \"returnAmount\", \"Sales\".\"payment_mode\" AS \"paymentMode\", \"Sales\".\"payment_status\" AS \"paymentStatus\", \"Sales\".\"payment_terms\" AS \"paymentTerms\", \"Sales\".\"shipping_address\" AS \"shippingAddress\", \"Sales\".\"billing_address\" AS \"billingAddress\", \"Sales\".\"is_billing_same_as_shipping\" AS \"isBillingSameAsShipping\", \"Sales\".\"is_billaddress\" AS \"isBillAddress\", \"Sales\".\"cod\", \"Sales\".\"notes\", \"Sales\".\"internal_notes\" AS \"internalNotes\", \"Sales\".\"terms\", \"Sales\".\"reason\", \"Sales\".\"service_items\" AS \"serviceItems\", \"Sales\".\"header_custom\" AS \"headerCustom\", \"Sales\".\"uuid\", \"Sales\".\"attachments\", \"Sales\".\"sales_data\" AS \"salesData\", \"Sales\".\"customer_id\" AS \"customerId\", \"Sales\".\"client_id\" AS \"clientId\", \"Sales\".\"service_id\" AS \"serviceId\", \"Sales\".\"company_id\" AS \"companyId\", \"Sales\".\"assigned_to\" AS \"assignedTo\", \"Sales\".\"created_by\" AS \"createdBy\", \"Sales\".\"updated_by\" AS \"updatedBy\", \"Sales\".\"is_active\" AS \"isActive\", \"Sales\".\"created_at\" AS \"createdAt\", \"Sales\".\"updated_at\" AS \"updatedAt\", \"Sales\".\"deleted_at\" AS \"deletedAt\", \"Sales\".\"created_at\", \"Sales\".\"updated_at\", \"Sales\".\"deleted_at\", \"Sales\".\"company_id\", \"Sales\".\"customer_id\", \"Sales\".\"client_id\", \"Sales\".\"service_id\", \"Sales\".\"assigned_to\", \"Sales\".\"created_by\", \"Sales\".\"updated_by\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"last_name\" AS \"customer.lastName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"sales\" AS \"Sales\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Sales\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Sales\".\"deleted_at\" IS NULL AND \"Sales\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Sales.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentSales (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1840:19)\n    at async Promise.all (index 8)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentLeads: column customer.contactNumber does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":121,"line":"3716","name":"error","position":"731","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;"},"sql":"SELECT \"Lead\".\"id\", \"Lead\".\"title\", \"Lead\".\"description\", \"Lead\".\"lead_date\" AS \"leadDate\", \"Lead\".\"assign_date\" AS \"assignDate\", \"Lead\".\"follow_up\" AS \"followUp\", \"Lead\".\"customer_id\" AS \"customerId\", \"Lead\".\"leadtype_id\" AS \"leadTypeId\", \"Lead\".\"leadstatus_id\" AS \"leadStatusId\", \"Lead\".\"assign_to\" AS \"assignTo\", \"Lead\".\"company_id\" AS \"companyId\", \"Lead\".\"source\", \"Lead\".\"notes\", \"Lead\".\"priority\", \"Lead\".\"status\", \"Lead\".\"estimated_value\" AS \"estimatedValue\", \"Lead\".\"created_by\" AS \"createdBy\", \"Lead\".\"updated_by\" AS \"updatedBy\", \"Lead\".\"created_at\" AS \"createdAt\", \"Lead\".\"updated_at\" AS \"updatedAt\", \"Lead\".\"deleted_at\" AS \"deletedAt\", \"customer\".\"id\" AS \"customer.id\", \"customer\".\"first_name\" AS \"customer.firstName\", \"customer\".\"contactNumber\" AS \"customer.contactNumber\" FROM \"leads\" AS \"Lead\" LEFT OUTER JOIN \"customers\" AS \"customer\" ON \"Lead\".\"customer_id\" = \"customer\".\"id\" AND (\"customer\".\"deleted_at\" IS NULL) WHERE (\"Lead\".\"deleted_at\" IS NULL AND \"Lead\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') ORDER BY \"createdAt\" DESC LIMIT 5;","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.select (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Lead.findAll (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async getRecentLeads (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1867:19)\n    at async Promise.all (index 9)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:00:40"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError in getRecentEstimations: column c.contact_number does not exist\u001b[39m","name":"SequelizeDatabaseError","original":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"parameters":{},"parent":{"code":"42703","file":"parse_relation.c","length":114,"line":"3716","name":"error","position":"44","routine":"errorMissingColumn","severity":"ERROR","sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5"},"sql":"SELECT e.*, c.first_name as customer_name, c.contact_number\n      FROM estimations e\n      LEFT JOIN customers c ON e.customer_id = c.id\n      WHERE e.company_id = '8722167d-d2ae-498f-95a3-c18afcf427ae'\n      ORDER BY e.created_at DESC\n      LIMIT 5","stack":"Error\n    at Query.run (D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\New_TrackNew\\track_new\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getRecentEstimations (file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:1738:25)\n    at async Promise.all (index 4)\n    at async file:///D:/New_TrackNew/track_new/backend/src/controllers/dashboardController.js:65:9","timestamp":"2025-07-02 14:04:01:41"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:34:01.480Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service_categories/stats\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:04:01:41"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T08:34:01.484Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customer-categories/stats\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 14:04:01:41"}
